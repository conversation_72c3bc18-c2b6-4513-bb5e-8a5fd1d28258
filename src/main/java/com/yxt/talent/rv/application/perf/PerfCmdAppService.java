package com.yxt.talent.rv.application.perf;

import com.yxt.ApplicationCommandService;
import com.yxt.common.exception.ApiException;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.talent.rv.controller.manage.perf.viewobj.PerfClearCheckVO;
import com.yxt.talent.rv.controller.manage.perf.viewobj.XpdManagerVO;
import com.yxt.talent.rv.controller.manage.perf.viewobj.XpdUsedProjectVO;
import com.yxt.talent.rv.controller.openapi.command.PerfSyncV2OpenCmd;
import com.yxt.talent.rv.domain.perf.Perf;
import com.yxt.talent.rv.domain.perf.PerfGrade;
import com.yxt.talent.rv.domain.perf.PerfGrade.DefaultLevel;
import com.yxt.talent.rv.domain.perf.PerfPeriod;
import com.yxt.talent.rv.domain.perf.repo.PerfDomainRepo;
import com.yxt.talent.rv.domain.perf.repo.PerfGradeDomainRepo;
import com.yxt.talent.rv.domain.perf.repo.PerfPeriodDomainRepo;
import com.yxt.talent.rv.domain.user.UserDomainRepo;
import com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys;
import com.yxt.talent.rv.infrastructure.common.utilities.util.EntityUtil;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.activity.ActivityPerfMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.activity.RvActivityParticipationRelationMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.perf.PerfGradeMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.perf.PerfMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.udp.UdpLiteUserMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.activity.ActivityPerfPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.activity.RvActivityParticipationRelationPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.perf.PerfGradePO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.udp.UdpLiteUserPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdPO;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.yxt.common.util.StreamUtil.list2map;
import static com.yxt.common.util.Validate.isTrue;
import static com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys.PREF_SYNC_ERROR_1;
import static com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys.PREF_SYNC_ERROR_2;
import static com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys.PREF_SYNC_PERIOD_INVALID_1;
import static com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys.PREF_SYNC_PERIOD_INVALID_2;
import static com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys.PREF_SYNC_PERIOD_INVALID_3;
import static com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys.PREF_SYNC_PERIOD_INVALID_4;
import static com.yxt.talent.rv.infrastructure.common.utilities.util.CommonUtil.mapListThenFilterNull;
import static java.util.function.Function.identity;
import static org.apache.commons.lang3.StringUtils.EMPTY;

@Slf4j
@RequiredArgsConstructor
@ApplicationCommandService
public class PerfCmdAppService {

    private final PerfGradeDomainRepo perfGradeDomainRepo;
    private final PerfDomainRepo perfDomainRepo;
    private final UdpLiteUserMapper udpLiteUserMapper;
    private final PerfPeriodDomainRepo perfPeriodDomainRepo;
    private final PerfGradeMapper perfGradeMapper;
    private final PerfMapper perfMapper;
    private final ActivityPerfMapper activityPerfMapper;
    private final XpdMapper xpdMapper;
    private final UserDomainRepo userDomainRepo;
    private final RvActivityParticipationRelationMapper rvActivityParticipationRelationMapper;

    /**
     * 初始化机构的绩效等级
     *
     * @param orgId
     */
    public void initOrgPerfGrade(String orgId) {
        // 查看是否有绩效
        Collection<PerfGradePO> perfGradePOS = perfGradeMapper.selectByOrgIdAll(orgId);
        if (CollectionUtils.isNotEmpty(perfGradePOS)) {
            log.info("LOG14385:initOrgPerfGrade perfGradePOS data orgId={}", orgId);
            return;
        }
        List<PerfGrade> list = new ArrayList<>();
        DefaultLevel[] levels = DefaultLevel.values();
        for (int i = 0; i < levels.length; i++) {
            PerfGrade perfGrade = new PerfGrade();
            perfGrade.setOrgId(orgId);
            perfGrade.setGradeName(levels[i].getName());
            perfGrade.setGradeValue(levels[i].getCode());
            perfGrade.setOrderIndex(i);
            EntityUtil.setAuditFields(perfGrade);
            list.add(perfGrade);
        }

        List<PerfGrade> perfGrades =
                list.stream().sorted(Comparator.comparing(PerfGrade::getGradeValue)).toList();
        int index = 0;
        for (PerfGrade perfGrade : perfGrades) {
            perfGrade.setOrderIndex(index++);
        }
        perfGrades.forEach(perfGradeDomainRepo::save);
    }

    /**
     * 处理同步用户绩效
     * 1. 如果绩效等级不存在，置空
     * 2，如果绩效周期不存在，创建一个绩效周期
     *
     * @param orgId
     * @param datas
     */
    public void syncUserPerf(String orgId, Collection<PerfSyncV2OpenCmd> datas) {
        doPreCheck(datas);
        Map<String, String> userIdDict = extractUserIdDict(orgId, datas);
        Map<String, PerfGrade> perfGradeNameDict = extractPerfGradeNameMap(orgId);
        Map<String, PerfPeriod> perfPeriodDict = extractPerfPeriodDict(orgId);
        Map<String, Perf> existingPerfs =
                loadExistingPerfs(orgId, userIdDict.values(), perfPeriodDict.values());
        List<Perf> perfs = mapListThenFilterNull(datas, data -> buildPerf(
            orgId, data, userIdDict, perfGradeNameDict, perfPeriodDict, existingPerfs
            )
        );

        //以防万一：根据orgId+userId+periodId分区去重
        Collection<Perf> saveInstances =
                list2map(perfs, e -> e.getOrgId() + e.getUserId() + e.getPerfPeriodId()).values();
        perfDomainRepo.save(saveInstances);

        // 同步绩效总分到周期表
        for (PerfSyncV2OpenCmd data : datas) {
            String key = buildOrgPeriodKey(orgId, data.getYearly(), data.getCycle(), data.getPeriod(), EMPTY);
            PerfPeriod perfPeriod = perfPeriodDict.get(key);
            if (perfPeriod != null && perfPeriod.getScoreTotal() == null && data.getPerfScore() != null) {
                perfPeriod.setScoreTotal(data.getPerfScore());
                perfPeriod.setUpdateTime(LocalDateTime.now());
            }
        }
        perfPeriodDomainRepo.save(perfPeriodDict.values());
    }

    private void doPreCheck(Collection<PerfSyncV2OpenCmd> datas) {
        for (PerfSyncV2OpenCmd data : datas) {
            Integer period = data.getPeriod();
            switch (data.getCycle()) {
                case 3 -> isTrue(Objects.equals(period, data.getYearly()),
                                 PREF_SYNC_PERIOD_INVALID_4);
                case 2 -> isTrue(period >= 1 && period <= 2, PREF_SYNC_PERIOD_INVALID_1);
                case 1 -> isTrue(period >= 1 && period <= 4, PREF_SYNC_PERIOD_INVALID_2);
                case 0 -> isTrue(period >= 1 && period <= 12, PREF_SYNC_PERIOD_INVALID_3);
                default -> throw new IllegalArgumentException("不支持的绩效周期类型");
            }

            BigDecimal perfPoint = data.getPerfPoint();
            String perf = data.getPerf();
            // 绩效得分和绩效等级必须有一个有值
            isTrue(perfPoint != null || StringUtils.isNotBlank(perf), PREF_SYNC_ERROR_1);
            // 当绩效得分时，必须要有绩效总分
            isTrue(perfPoint == null || data.getPerfScore() != null, PREF_SYNC_ERROR_2);
        }
    }

    private Map<String, Perf> loadExistingPerfs(
            String orgId, Collection<String> userIds, Collection<PerfPeriod> periods) {
        List<String> perfPeriodIds = mapListThenFilterNull(periods, PerfPeriod::getId);
        return perfDomainRepo.loadByUserIdsAndPeriodIds(orgId, userIds, perfPeriodIds).stream()
                .collect(Collectors.toMap(
                        perf -> buildUserPerfKey(perf.getOrgId(), perf.getUserId(),
                                                 perf.getPerfPeriodId()), identity(), (a, b) -> a));
    }

    private static @Nonnull String buildUserPerfKey(
            String orgId, String userId, String perfPeriodId) {
        return orgId + ":" + userId + ":" + perfPeriodId;
    }

    private Map<String, PerfPeriod> extractPerfPeriodDict(String orgId) {
        Collection<PerfPeriod> perfPeriods = perfPeriodDomainRepo.load(orgId);
        return list2map(perfPeriods,
                        e -> buildOrgPeriodKey(orgId, e.getYearly(), e.getCycle(), e.getPeriod(),
                                               e.buildPeriodName()));
    }

    private String buildOrgPeriodKey(
            String orgId, Integer yearly, Integer cycle, Integer period, String periodName) {
        String sYearly = yearly == null ? EMPTY : String.valueOf(yearly);
        String sCycle = cycle == null ? EMPTY : String.valueOf(cycle);
        String sPeriod = period == null ? EMPTY : String.valueOf(period);
        if (StringUtils.isBlank(sYearly + sCycle + sPeriod)) {
            return orgId + periodName;
        }
        // 新绩效周期，必有年份、周期、期数
        return orgId + ":" + sYearly + ":" + sCycle + ":" + sPeriod;
    }

    private Map<String, PerfGrade> extractPerfGradeNameMap(String orgId) {
        Collection<PerfGrade> perfGrades = perfGradeDomainRepo.load(orgId);
        return list2map(perfGrades, PerfGrade::getGradeName);
    }

    private Map<String, String> extractUserIdDict(
            String orgId, Collection<PerfSyncV2OpenCmd> datas) {
        List<String> thirdUserIds =
                datas.stream().map(PerfSyncV2OpenCmd::getThirdUserId).distinct().toList();
        List<UdpLiteUserPO> udpLiteUserPOs =
                udpLiteUserMapper.selectByThirdUserIds(orgId, thirdUserIds);
        return list2map(udpLiteUserPOs, UdpLiteUserPO::getThirdUserId, UdpLiteUserPO::getId);
    }


    @Nullable
    private Perf buildPerf(
            String orgId, PerfSyncV2OpenCmd userPerf, Map<String, String> userIdDict,
            Map<String, PerfGrade> perfGradeNameDict, Map<String, PerfPeriod> perfPeriodDict,
            Map<String, Perf> existingPerfs) {
        // 查找对应的userId
        String thirdUserId = userPerf.getThirdUserId();
        String userId = userIdDict.get(thirdUserId);
        if (StringUtils.isBlank(userId)) {
            log.warn("LOG14035:用户[{}]在机构[{}]中不存在", thirdUserId, orgId);
            return null;
        }

        // 查找对应的绩效周期，没有则创建一个
        Integer yearly = userPerf.getYearly();
        Integer cycle = userPerf.getCycle();
        Integer period = userPerf.getPeriod();
        String periodMd5Key = buildOrgPeriodKey(orgId, yearly, cycle, period, EMPTY);

        String perfPeriodId;
        if (!perfPeriodDict.containsKey(periodMd5Key)) {
            PerfPeriod perfPeriod = new PerfPeriod();
            perfPeriod.setOrgId(orgId);
            perfPeriod.setYearly(yearly);
            perfPeriod.setCycle(cycle);
            perfPeriod.setPeriod(period);
            perfPeriod.setOrderIndex(findMaxOrderIndex(perfPeriodDict) + 10);
            EntityUtil.setAuditFields(perfPeriod);
            perfPeriodDomainRepo.save(perfPeriod);
            perfPeriodDict.put(periodMd5Key, perfPeriod);
            perfPeriodId = perfPeriod.getId();
        } else {
            perfPeriodId = perfPeriodDict.get(periodMd5Key).getId();
        }

        // 查找该员工在当前绩效周期下的绩效，没有则创建一个
        String finalPerfPeriodId = perfPeriodId;
        String userPerfKey = buildUserPerfKey(orgId, userId, finalPerfPeriodId);
        Perf perf = existingPerfs.computeIfAbsent(userPerfKey, (k) -> {
            Perf newInstance = new Perf();
            newInstance.setOrgId(orgId);
            newInstance.setUserId(userId);
            newInstance.setPerfPeriodId(finalPerfPeriodId);
            newInstance.setNewInstance(true);
            EntityUtil.setAuditFields(newInstance);
            return newInstance;
        });
        perf.setPerfPeriodId(perfPeriodId);

        // 将绩效等级文本（S/A/B/C）转换成对应的绩效标识（101/102/103等）, 为了兼容绩效业务表
        Integer perfGradeValue = Optional.ofNullable(perfGradeNameDict.get(userPerf.getPerf()))
                .map(PerfGrade::getGradeValue).orElse(0);
        perf.setPerfGradeValue(perfGradeValue);
        perf.setPerfScore(userPerf.getPerfScore());
        perf.setPerfPoint(userPerf.getPerfPoint());
        perf.setPerfThirdActivityId(userPerf.getPerfThirdActivityId());
        perf.setPerfActivity(userPerf.getPerfActivity());
        EntityUtil.setUpdate(perf);
        perf.setDeleted(userPerf.getDeleted());
        return perf;
    }

    private Integer findMaxOrderIndex(Map<String, PerfPeriod> perfPeriodDict) {
        return perfPeriodDict.values()
            .stream()
            .map(PerfPeriod::getOrderIndex)
            .filter(Objects::nonNull)
            .max(Comparator.naturalOrder())
            .orElse(0);
    }

    /**
     * 检查是否可以清空绩效数据
     *
     * @param userCache 用户信息
     * @return 检查结果
     */
    public PerfClearCheckVO checkPerfClear(UserCacheDetail userCache) {
        String orgId = userCache.getOrgId();

        // 查找所有使用绩效数据的活动
        List<ActivityPerfPO> perfActivities = activityPerfMapper.selectByOrgId(orgId);
        if (CollectionUtils.isEmpty(perfActivities)) {
            return new PerfClearCheckVO(1, Collections.emptyList());
        }

        // 获取所有活动ID
        List<String> activityIds = perfActivities.stream()
                .map(ActivityPerfPO::getAomActId)
                .collect(Collectors.toList());

        // 查找使用这些活动的未结束盘点项目
        List<XpdPO> activeProjects = xpdMapper.findActiveProjectsByActivityIds(orgId, activityIds);
        if (CollectionUtils.isEmpty(activeProjects)) {
            return new PerfClearCheckVO(1, Collections.emptyList());
        }

        // 获取项目对应的活动ID列表
        Map<String, String> xpdToActvIdMap = activeProjects.stream()
                .collect(Collectors.toMap(XpdPO::getId, XpdPO::getAomPrjId));

        // 获取项目负责人信息 - 使用批量查询优化性能
        Map<String, List<RvActivityParticipationRelationPO>> projectManagersMap = new HashMap<>();

        // 获取所有活动ID列表
        List<String> actvIds = new ArrayList<>(xpdToActvIdMap.values());

        // 如果有活动ID，则进行批量查询
        if (!CollectionUtils.isEmpty(actvIds)) {
            // 批量查询所有活动的负责人
            List<RvActivityParticipationRelationPO> allManagers = rvActivityParticipationRelationMapper.findManagersByActvIds(orgId, actvIds);

            // 按活动ID分组
            Map<String, List<RvActivityParticipationRelationPO>> managersByActvId = allManagers.stream()
                    .collect(Collectors.groupingBy(RvActivityParticipationRelationPO::getActvId));

            // 将分组结果映射到项目ID
            for (Map.Entry<String, String> entry : xpdToActvIdMap.entrySet()) {
                String xpdId = entry.getKey();
                String actvId = entry.getValue();
                List<RvActivityParticipationRelationPO> managers = managersByActvId.get(actvId);
                if (!CollectionUtils.isEmpty(managers)) {
                    projectManagersMap.put(xpdId, managers);
                }
            }
        }

        // 转换为VO对象
        List<XpdUsedProjectVO> projectVOs = activeProjects.stream()
                .map(project -> {
                    // 使用活动状态作为盘点项目状态
                    Integer status = project.getActvStatus();
                    if (status == null) {
                        status = 1; // 默认使用未发布状态
                    }

                    // 获取项目负责人信息
                    List<XpdManagerVO> managerVOs = new ArrayList<>();
                    List<RvActivityParticipationRelationPO> managers = projectManagersMap.get(project.getId());
                    if (!CollectionUtils.isEmpty(managers)) {
                        // 将所有负责人添加到列表中
                        managerVOs = managers.stream()
                                .map(manager -> XpdManagerVO.builder()
                                        .managerId(manager.getUserId())
                                        .managerName(manager.getFullname())
                                        .build())
                                .collect(Collectors.toList());
                    }

                    return XpdUsedProjectVO.builder()
                            .xpdId(project.getId())
                            .xpdName(project.getXpdName())
                            .status(status)
                            .managers(managerVOs)
                            .build();
                })
                .collect(Collectors.toList());

        return new PerfClearCheckVO(0, projectVOs);
    }

    /**
     * 清空绩效数据
     *
     * @param userCache 用户信息
     * @param force     是否强制清空（即使有未结束的盘点项目）
     */
    @Transactional(rollbackFor = Exception.class)
    public void clearPerfData(UserCacheDetail userCache, boolean force) {
        String orgId = userCache.getOrgId();
        String userId = userCache.getUserId();

        // 如果不是强制清空，先检查是否有未结束的盘点项目
        if (!force) {
            PerfClearCheckVO checkResult = checkPerfClear(userCache);
            if (checkResult.getCanClear() == 0) {
                throw new ApiException(ExceptionKeys.PERF_CLEAR_ACTIVE_PROJECTS);
            }
        }

        // 记录日志
        log.info("LOG20673:清空绩效数据，orgId={}, userId={}, force={}", orgId, userId, force);

        // 执行清空操作
        int deletedCount = perfMapper.deleteAllByOrgId(orgId);

        log.info("LOG20663:绩效数据清空完成，orgId={}, 删除记录数={}", orgId, deletedCount);
    }
}
