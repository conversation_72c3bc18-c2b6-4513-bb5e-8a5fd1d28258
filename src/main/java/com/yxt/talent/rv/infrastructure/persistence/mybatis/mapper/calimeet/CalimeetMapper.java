package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calimeet;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yxt.talent.rv.controller.client.general.meet.query.MeetClientQuery;
import com.yxt.talent.rv.controller.client.general.meet.viewobj.MeetClientVO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.CommonMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CalimeetPO;
import jakarta.annotation.Nonnull;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface CalimeetMapper extends CommonMapper<CalimeetPO> {
    int insert(CalimeetPO record);

    int batchInsert(@Param("list") List<CalimeetPO> list);

    void insertOrUpdate(@Param("element") CalimeetPO element);

    void updateById(@Nonnull CalimeetPO calimeetPO);

    int countByOrgIdAndId(@Nonnull @Param("orgId") String orgId, @Nonnull @Param("id") String id);

    CalimeetPO selectByIdAndOrgId(@Nonnull @Param("id") String id, @Nonnull @Param("orgId") String orgId);

    void deleteByIdAndOrgId(@Param("orgId") String orgId, @Param("id") String caliMeetId);

    IPage<CalimeetPO> pageQuery(Page<CalimeetPO> requestPage, @Param("orgId") String orgId,
            @Param("xpdId") String xpdId, @Param("name") String name, @Param("statusList") List<Integer> statusList,
            @Param("authIds") List<String> authUserIds);

    void deleteMeetingByXpdId(@Param("orgId") String orgId, @Param("xpdId") String xpdId,
            @Param("operatorId") String operatorId);

    IPage<MeetClientVO> clientPage(Page<MeetClientVO> requestPage, @Param("orgId") String orgId,
            @Param("userId") String userId, @Param("search") MeetClientQuery search);

    List<CalimeetPO> selectByOrgIdAndIds(@Param("orgId") String orgId, @Param("ids") List<String> ids);

    /**
     * 查询需要刷新会议结果的校准会ID
     * @return
     */
    List<String> select4RefreshCaliMeetUserResult();

    CalimeetPO selectById(@Param("id") String id);

    List<CalimeetPO> selectListByXpdIdAndOrgId(@Param("xpdId") String xpdId, @Param("orgId") String orgId);
}