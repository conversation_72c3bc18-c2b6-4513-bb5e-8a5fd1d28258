package com.yxt.talent.rv.controller.manage.calimeet;

import com.google.common.collect.Lists;
import com.yxt.aom.base.util.AomNumberUtils;
import com.yxt.auditlog.annotation.Auditing;
import com.yxt.common.Constants;
import com.yxt.common.annotation.Auth;
import com.yxt.common.enums.AuthType;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.common.service.AuthService;
import com.yxt.common.util.ApiUtil;
import com.yxt.modelhub.api.bean.dto.AmSlDrawer4RespDTO;
import com.yxt.modelhub.api.bean.dto.AmUser4DTO;
import com.yxt.modelhub.api.bean.dto.SearchDTO;
import com.yxt.modelhub.api.utils.QueryUtil;
import com.yxt.spsdk.audit.annotations.EasyAuditLog;
import com.yxt.spsdk.audit.annotations.EasyAuditLogSelect;
import com.yxt.talent.rv.application.calimeet.CaliMeetRecordService;
import com.yxt.talent.rv.application.xpd.common.dto.CaliDimResultResp;
import com.yxt.talent.rv.controller.manage.calimeet.query.CaliRecord4Query;
import com.yxt.talent.rv.controller.manage.calimeet.query.CaliRecordExport4Query;
import com.yxt.talent.rv.controller.manage.calimeet.viewobj.CaliMeetRecordVO;
import com.yxt.talent.rv.controller.manage.calimeet.viewobj.Dimcombitionrecord4Get;
import com.yxt.talent.rv.infrastructure.common.constant.RvAuditLogConstants;
import com.yxt.talent.rv.infrastructure.common.utilities.util.ApassEntityUtils;
import com.yxt.talent.rv.infrastructure.common.utilities.util.CommonUtil;
import com.yxt.talent.rv.infrastructure.common.utilities.util.DateTimeUtil;
import com.yxt.talent.rv.infrastructure.service.file.viewobj.GenericApaasFileExportVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

import static com.yxt.talent.rv.infrastructure.common.constant.AuthCodes.AUTH_CODE_ALL;

/**
 * <AUTHOR>
 * @description
 * @date 2025/5/14
 */
@Slf4j
@RestController
@RequestMapping("/mgr/cali/record")
@RequiredArgsConstructor
@Tag(name = "校准记录", description = "校准记录")
public class CaliRecordController {

    private final AuthService authService;
    private final CaliMeetRecordService caliMeetRecordService;

    @Parameters(value = {@Parameter(name = "offset", description = "从第几条开始取 默认0", in = ParameterIn.QUERY),
            @Parameter(name = "limit", description = "每页多少条 默认20", in = ParameterIn.QUERY)})
    @Operation(summary = "维度组合校准记录列表")
    @PostMapping(value = "/app/search", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    @Auth(action = Constants.LOG_TYPE_GETLIST, type = {AuthType.TOKEN})
    public PagingList<Dimcombitionrecord4Get> pageList(HttpServletRequest request, @RequestBody SearchDTO bean) {
        QueryUtil.Search search = QueryUtil.parse(bean);
        CaliRecord4Query query = convertCaliRecordUserQuery(search);
        PagingList<CaliMeetRecordVO> listPage = this.list(query);
        PagingList<Dimcombitionrecord4Get> resPage = new PagingList<>();
        List<CaliMeetRecordVO> datas = listPage.getDatas();
        List<Dimcombitionrecord4Get> resList = new ArrayList<>();
        for (CaliMeetRecordVO data : datas) {
            Dimcombitionrecord4Get record = new Dimcombitionrecord4Get();
            record.setUserId(buildUserInfo(data));
            // 维度组合
            record.setDimComb(ApassEntityUtils.createAmSlDrawerIdName(data.getDimCombId(), data.getDimCombName()));
            // 校准幅度
            record.setCalibNum(Long.valueOf(data.getCaliShift()));
            record.setBeforeGrid(data.getOriginalCellIndexName());
            record.setAfterGrid(data.getCellIndexName());
            record.setCalibTime(DateTimeUtil.makeLocalDateTime2Date(data.getCaliTime()));
            record.setCalibrator(ApassEntityUtils.createAmSlDrawerIdName(data.getCaliUserId(), data.getCaliUserName()));
            record.setCalibProjId(
                    ApassEntityUtils.createAmSlDrawerIdName(data.getCaliMeetId(), data.getCaliMeetName()));
            //record.setStatus();
            resList.add(record);
        }
        resPage.setDatas(resList);
        resPage.setPaging(listPage.getPaging());
        return resPage;
    }

    /*@Parameters(value = {
        @Parameter(name = "offset", description = "从第几条开始取 默认0", in = ParameterIn.QUERY),
        @Parameter(name = "limit", description = "每页多少条 默认20", in = ParameterIn.QUERY)})
    @Operation(summary = "校准记录列表")
    @PostMapping(value = "/app/search", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    @Auth(action = Constants.LOG_TYPE_GETLIST, type = {AuthType.TOKEN})
    public PagingList<Calibraterecord4Get> pageList(HttpServletRequest request, @RequestBody SearchDTO bean) {
        UserCacheBasic userCacheBasic = authService.getUserCacheBasic();
        QueryUtil.Search search = QueryUtil.parse(bean);
        CaliRecordQueryQuery query = convertCaliRecordUserQuery(search);
        PagingList<CaliMeetRecordVO> list = this.list(query);
        List<CaliMeetRecordVO> datas = list.getDatas();
        PagingList<Calibraterecord4Get> resPage = new PagingList<>();
        List<Calibraterecord4Get> resList = new ArrayList<>();
        for (CaliMeetRecordVO data : datas) {
            Calibraterecord4Get res = new Calibraterecord4Get();
            res.setId(data.getId());
            res.setName(data.getDeptName());

            res.setUserId(buildUserInfo(data));
            res.


        }
        return demoService.pageList(userCacheBasic.getOrgId(), bean);
    }*/

    private AmSlDrawer4RespDTO buildUserInfo(CaliMeetRecordVO obj) {
        AmSlDrawer4RespDTO userId = new AmSlDrawer4RespDTO();
        List<Object> datas = Lists.newArrayList();
        userId.setDatas(datas);

        AmUser4DTO.UserInfo userInfo = new AmUser4DTO.UserInfo();
        userInfo.setId(obj.getUserId());
        userInfo.setUserName(obj.getUsername());
        userInfo.setName(obj.getFullname());
       /* userInfo.setImgUrl(obj.getImgUrl());
        userInfo.setUserNo(obj.getUserNo());*/
        // 0-禁用 1-启用 2-删除
        userInfo.setStatus(String.valueOf(obj.getStatus()));

        AmUser4DTO.AmDept deptList = new AmUser4DTO.AmDept();
        List<AmUser4DTO.DeptInfo> deptInfoDatas = Lists.newArrayList();
        deptList.setDatas(deptInfoDatas);
        AmUser4DTO.DeptInfo deptInfo = new AmUser4DTO.DeptInfo();
        //deptInfo.setId(obj.getDeptId());
        deptInfo.setName(obj.getDeptName());
        deptInfoDatas.add(deptInfo);

        userInfo.setDeptList(deptList);

        AmUser4DTO.AmPosition positionList = new AmUser4DTO.AmPosition();
        List<AmUser4DTO.PositionInfo> positionInfoDatas = Lists.newArrayList();
        positionList.setDatas(positionInfoDatas);
        AmUser4DTO.PositionInfo positionInfo = new AmUser4DTO.PositionInfo();
        //positionInfo.setId(obj.getPositionId());
        positionInfo.setName(obj.getPositionName());
        positionInfoDatas.add(positionInfo);

        userInfo.setPositionList(positionList);

        AmUser4DTO.AmGrade gradeList = new AmUser4DTO.AmGrade();
        List<AmUser4DTO.GradeInfo> gradeInfoDatas = Lists.newArrayList();
        gradeList.setDatas(gradeInfoDatas);
        AmUser4DTO.GradeInfo gradeInfo = new AmUser4DTO.GradeInfo();
        gradeInfo.setName(obj.getGradeName());
        gradeInfoDatas.add(gradeInfo);

        userInfo.setGradeList(gradeList);

        datas.add(userInfo);

        return userId;
    }


    private CaliRecord4Query convertCaliRecordUserQuery(QueryUtil.Search search) {
        //CaliMeetUserQuery queryParam = new CaliMeetUserQuery();
        CaliRecord4Query queryParam = new CaliRecord4Query();
        List<String> calibProjIds = search.getFilterIn().get("calibProjId");
        if (CollectionUtils.isNotEmpty(calibProjIds)) {
            queryParam.setCaliMeetIds(calibProjIds);
        }

        String dimCombId = search.getFilterEq().get("dim");
        if (StringUtils.isNotBlank(dimCombId)) {
            queryParam.setDimId(dimCombId);
        }

        String caliStatusStr = search.getFilterEq().get("status");
        if (StringUtils.isNotBlank(caliStatusStr) && AomNumberUtils.isNumber(caliStatusStr)) {
            queryParam.setUserStatus(Integer.valueOf(caliStatusStr));
        }

        String positionId = search.getFilterEq().get("userId.positionId");
        if (StringUtils.isNotBlank(positionId)) {
            queryParam.setPosIds(Lists.newArrayList(positionId));
        }

        String deptId = search.getFilterEq().get("userId.deptId");
        if (StringUtils.isNotBlank(deptId)) {
            queryParam.setScopeDeptIds(Lists.newArrayList(deptId));
        }

        String gradeId = search.getFilterEq().get("userid.gradeId");
        if (StringUtils.isNotBlank(gradeId)) {
            queryParam.setGradeIds(Lists.newArrayList(gradeId));
        }

        /*String gradeId = search.getFilterEq().get("userid.gradeId");
        if (StringUtils.isNotBlank(gradeId)) {
            queryParam.setGradeIds(Lists.newArrayList(gradeId));
        }*/

        String searchKey = ApiUtil.getFiltedLikeString(search.getSearch().getValue());
        if (StringUtils.isNotBlank(searchKey)) {
            queryParam.setSearchKey(searchKey);
            queryParam.setKwType(CommonUtil.getKwType(search));
        }
        queryParam.setOpenAuth(false);
        return queryParam;
    }

    @PostMapping("/list")
    @Operation(summary = "获取校准人员列表")
    @Auth(codes = {AUTH_CODE_ALL})
    public PagingList<CaliMeetRecordVO> list(@RequestBody CaliRecord4Query query) {
        UserCacheDetail userCacheDetail = authService.getUserCacheDetail();
        return caliMeetRecordService.list(userCacheDetail.getOrgId(), query);
    }


    @PostMapping("/export")
    @Operation(summary = "导出校准记录")
    @Auth(codes = {AUTH_CODE_ALL})
    @Auditing
    @EasyAuditLogSelect({
            @EasyAuditLog(value = RvAuditLogConstants.CALI_RECORD_EXPORT, paramExp = "#query.caliMeetId", conditionExp = "#query.caliMeetId != ''"),
            @EasyAuditLog(value = RvAuditLogConstants.XPD_CALI_RECORD_EXPORT, paramExp = "#query.xpdId", conditionExp = "#query.caliMeetId ==''")})
    public GenericApaasFileExportVO export(HttpServletRequest request, @RequestBody CaliRecordExport4Query query) {
        UserCacheDetail userCacheDetail = authService.getUserCacheDetail();
        return caliMeetRecordService.export(userCacheDetail.getOrgId(), query.getCaliMeetId());
    }

    @PostMapping("/{caliMeetId}/{id}/detail")
    @Parameter(name = "caliMeetId", description = "校准会id", required = true, in = ParameterIn.QUERY)
    @Parameter(name = "id", description = "校准记录列表id", required = true, in = ParameterIn.QUERY)
    @Operation(summary = "获取校准记录信息")
    @Auth(codes = {AUTH_CODE_ALL})
    public CaliDimResultResp queryUserDetail(HttpServletRequest request, @PathVariable String caliMeetId,
            @PathVariable String id) {
        UserCacheDetail userCacheDetail = authService.getUserCacheDetail();
        return caliMeetRecordService.queryCaliResult(userCacheDetail.getOrgId(), caliMeetId, id);
    }


}
