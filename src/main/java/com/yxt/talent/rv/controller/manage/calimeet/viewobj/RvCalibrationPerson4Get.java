package com.yxt.talent.rv.controller.manage.calimeet.viewobj;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Maps;
import com.yxt.modelhub.api.bean.dto.AmSlDrawer4RespDTO;
import com.yxt.modelhub.api.bean.dto.AmUser4DTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.Map;
@Getter
@Setter
@Schema(description = "校准人员详情返回")
public class RvCalibrationPerson4Get implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description="主键;id主键")
    private String id;
    @Schema(description="机构号;机构id")
    private String orgId;
    @Schema(description="名称")
    private String name;
    @Schema(description="父实体id;子实体选择的父实体id或者引用对象的实体id")
    private String calibrationId;
    @Schema(description="父实体id展示信息")
    @JsonProperty("@calibrationId")
    private Calibration4Get calibrationId__Record;
    @Schema(description="删除标识;0-未删除，1-已删除")
    private Integer deleted;
    @Schema(description="创建时间")
    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private Date createTime;
    @Schema(description="更新时间")
    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private Date updateTime;
    @Schema(description="创建人;创建人id")
    @JsonProperty("@createUserId")
    private AmUser4DTO createUserId;
    @Schema(description="更新人;更新人id")
    @JsonProperty("@updateUserId")
    private AmUser4DTO updateUserId;
    @Schema(description="被校准人员;按照业务需求,返回应用的实体字段")
    @JsonProperty("@userId")
    private AmSlDrawer4RespDTO userId;
    @JsonProperty("userId")
    private String uid;
    @Schema(description="校准状态")
    private String caliStatus;
    @Schema(description="人员状态")
    private String status;
    @Schema(description="最近一次校准记录;按照业务需求,返回应用的实体字段")
    @JsonProperty("@recordId")
    private AmSlDrawer4RespDTO recordId;

    @Schema(description="扩展字段")
    private Map<String,Object> _spares = Maps.newHashMap();
}
