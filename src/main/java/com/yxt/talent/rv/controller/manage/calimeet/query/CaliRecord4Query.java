package com.yxt.talent.rv.controller.manage.calimeet.query;

import com.yxt.talent.rv.controller.common.query.SearchUdpScopeAuthQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 *
 * @date 2025/5/14
 */
@Data
public class CaliRecord4Query extends SearchUdpScopeAuthQuery {

    @Schema(description = "校准会id")
    private List<String> caliMeetIds;

    @Schema(description = "维度")
    private String dimId;

    @Schema(description = "盘点项目id")
    private String xpdId;

    // 根据维度id转化的维度组合id
    private List<String> dimCombIds;



}
