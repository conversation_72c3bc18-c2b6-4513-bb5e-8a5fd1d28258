package com.yxt.talent.rv.controller.manage.xpd.rule.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
public enum DimLevelTypeEnum {

    /**
     * 人员分层方式
     */
    RATIO_VALUE(0, "按比例"),
    FIXED_VALUE(1, "按固定值"),
    JUDGE_RULE(2, "综合判断"),
    ;

    private final Integer code;
    private final String name;

    DimLevelTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static boolean byRatio(Integer code) {
        return RATIO_VALUE.getCode().equals(code);
    }

    public static boolean byFixedValue(Integer code) {
        return FIXED_VALUE.getCode().equals(code);
    }

    public static DimLevelTypeEnum getDefault() {
        return RATIO_VALUE;
    }

    public static DimLevelTypeEnum getByCode(int code) {
        for (DimLevelTypeEnum value : DimLevelTypeEnum.values()) {
            if (value.code == code) {
                return value;
            }
        }
        return RATIO_VALUE;
    }
}
