package com.yxt.talent.rv.application.xpd.common.dto;

import com.yxt.spsdk.common.bean.SpRuleBean;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

/**
 * 维度的分层规则
 *
 * <AUTHOR>
 * @date 2024/12/9 9:49
 */
@Getter
@Setter
public class XpdDimLevelRuleDto extends LevelBase {

    @Schema(description = "层级ID")
    private String gridLevelId;

    @Schema(description = "层级名称")
    private String levelName;

    @Schema(description = "层级的值，人员占比或得分")
    private BigDecimal levelValue;

    @Schema(description = "层级的值，人员占比或得分[内部用，不对前端展示，前端用levelValue]")
    private BigDecimal baseValue;

    /**
     * 匹配值，用于绩效等级
     */
    @Schema(description = "绩效结果[绩效]，值为活动结果ID列表")
    private List<String> matchValues;

    private List<XpdDimPerfResultDto> perfresults;

    /**
     * 匹配值，用于绩效等级
     */
    @Schema(description = "绩效结果[绩效]")
    private List<XpdDimPerfResultDto> matchValueList;

    @Schema(description = "排序")
    private Integer orderIndex;

    private String id;

    private String name;

    @Schema(description = "综合判断的规则")
    private SpRuleBean judgeRule;
}
