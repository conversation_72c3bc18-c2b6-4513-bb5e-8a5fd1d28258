package com.yxt.talent.rv.application.xpd.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 新盘点-项目规则配置出参Bean
 *
 * <AUTHOR>
 * @date 2024/12/16 15:35
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@Slf4j
@ToString(callSuper = true)
@Schema(description = "项目规则页")
public class XpdRuleInDto {

    @Schema(description = "盘点项目ID")
    private String xpdId;

    @Schema(description = "项目规则ID", hidden = true)
    private String xpdRuleId;

    @Schema(description = "计算方式 0-按维度结果计算 1-按指标结果计算")
    private Integer calcType;

    @Schema(description = "结果类型 0-维度分层结果 1-(维度/指标)得分 2-(维度/指标)达标率")
    private Integer resultType;

    @Schema(description = "计算规则 0:快捷配置 1:高级公式")
    private Integer calcRule;

    @Schema(description = "高级公式")
    private String formula;

    @Schema(description = "高级公式-展示")
    private String formulaDisplay;

    @Schema(description = "高级公式-数组展示")
    private List<FormulaExpression> formulaExpressions;

    @Schema(description = "高级公式-数组展示")
    private List<String> formulaExpCodes;

    @Schema(description = "分层方式 0：按比例 1：按固定值 2:综合判断")
    private Integer levelType;

    @Schema(description = "分层优先级 0: 高等级优先 1: 低等级优先")
    private Integer levelPriority;

    @Schema(description = "规则说明")
    private String ruleDesc;

    @Schema(description = "<按维度结果计算>时表示维度计算规则，<按指标结果计算>时表示指标计算规则")
    private List<XpdRuleCalcDto> ruleCalcList;

    @Schema(description = "分层规则")
    private List<XpdLevelRuleDto> levelRuleList;
}
