package com.yxt.talent.rv.controller.manage.meet.viewobj;

import com.yxt.talent.rv.controller.manage.prj.user.viewobj.PrjUserResultVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import java.util.List;


@Slf4j
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
public class MeetResultDetailVO {

    @Schema(description = "校准九宫格")
    List<PrjUserResultVO> calibrationList;

    @Schema(description = "校准会路径是否开启 0 关闭 1 开启")
    private Integer meetRouteStatus = 1;

    private Integer calimeetType;
}
