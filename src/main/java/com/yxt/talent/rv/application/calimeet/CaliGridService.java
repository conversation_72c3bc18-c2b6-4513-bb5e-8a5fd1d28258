package com.yxt.talent.rv.application.calimeet;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yxt.common.pojo.api.PageRequest;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.util.ApiUtil;
import com.yxt.common.util.BeanCopierUtil;
import com.yxt.common.util.StreamUtil;
import com.yxt.common.util.Validate;
import com.yxt.spsdfacade.bean.spsd.DimensionList4Get;
import com.yxt.spsdk.common.utils.CommonUtils;
import com.yxt.talent.rv.application.calimeet.dto.CaliIndexNumDTO;
import com.yxt.talent.rv.application.calimeet.dto.CaliLastRecordUserDTO;
import com.yxt.talent.rv.application.calimeet.dto.CaliUserIndexDTO;
import com.yxt.talent.rv.application.meet.dto.CaliMeetDimDTO;
import com.yxt.talent.rv.application.xpd.common.dto.CaliDimResultDto;
import com.yxt.talent.rv.application.xpd.common.dto.CaliDimResultResp;
import com.yxt.talent.rv.application.xpd.common.dto.CaliDimResultWrapDto;
import com.yxt.talent.rv.application.xpd.common.dto.CaliUpdateUserResultReq;
import com.yxt.talent.rv.application.xpd.common.enums.CaliResultTypeEnum;
import com.yxt.talent.rv.application.xpd.result.XpdResultCalcService;
import com.yxt.talent.rv.application.xpd.xpd.XpdAppService;
import com.yxt.talent.rv.application.xpd.xpd.XpdService;
import com.yxt.talent.rv.controller.manage.calimeet.command.CaliMeetMoveCmd;
import com.yxt.talent.rv.controller.manage.calimeet.query.CaliResult4Query;
import com.yxt.talent.rv.controller.manage.calimeet.viewobj.CaliCellUserVO;
import com.yxt.talent.rv.controller.manage.calimeet.viewobj.CaliGridViewVO;
import com.yxt.talent.rv.controller.manage.meet.viewobj.MeetResultDetailVO;
import com.yxt.talent.rv.controller.manage.meet.viewobj.MeetUserDimResultVO;
import com.yxt.talent.rv.controller.manage.prj.user.viewobj.PrjUserResultVO;
import com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys;
import com.yxt.talent.rv.infrastructure.common.utilities.util.MathUtil;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calimeet.CalimeetMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calimeet.CalimeetRecordMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calimeet.CalimeetResultUserDimMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calimeet.CalimeetUserMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.udp.UdpLiteUserMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.*;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CalimeetPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CalimeetResultUserDimPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.udp.UdpLiteUserPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.*;
import com.yxt.talent.rv.infrastructure.service.remote.L10nAclService;
import com.yxt.talent.rv.infrastructure.service.remote.SpsdAclService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.yxt.common.util.Validate.isNotEmpty;
import static com.yxt.common.util.Validate.isNotNull;
import static com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys.XPD_GRID_CELL_NOT_FOUND;
import static com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys.XPD_GRID_NOT_FOUND;
import static com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys.XPD_RULE_CONF_NOT_FOUND;

/**
 * <AUTHOR>
 * @description
 * @date 2025/5/21
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class CaliGridService {
    private final XpdRuleConfMapper xpdRuleConfMapper;
    private final XpdGridCellMapper xpdGridCellMapper;
    private final XpdGridMapper xpdGridMapper;
    private final XpdGridRatioMapper xpdGridRatioMapper;
    private final CalimeetMapper calimeetMapper;
    private final CalimeetUserMapper calimeetUserMapper;
    private final UdpLiteUserMapper udpLiteUserMapper;
    private final XpdGridLevelMapper xpdGridLevelMapper;
    private final XpdResultCalcService xpdResultCalcService;
    private final XpdDimCombMapper xpdDimCombMapper;
    private final XpdMapper xpdMapper;
    private final L10nAclService l10nAclService;
    private final XpdDimMapper xpdDimMapper;
    private final CalimeetResultUserDimMapper calimeetResultUserDimMapper;
    private final CalimeetRecordMapper calimeetRecordMapper;
    private final SpsdAclService spsdAclService;

    public List<CaliGridViewVO> caliGridView(String orgId, CaliResult4Query query) {
        String xpdId = query.getProjectId();
        XpdGridPO xpdGrid = selectXpdGrid(orgId, xpdId);
        String gridId = xpdGrid.getId();
        String dimCombId = query.getDimCombId();
        String caliMeetId = query.getMeetingId();
        // 是否开启比例控制
        CalimeetPO calimeet = calimeetMapper.selectByIdAndOrgId(query.getMeetingId(), orgId);
        Validate.isNotNull(calimeet, ExceptionKeys.CALI_MEET_NOT_EXISTED);
        // 宫格内是否设置了占比
        if (calimeet.getShowRatio() == 0) {
            return new ArrayList<>();
        }

        // Get grid cells based on config type
        List<XpdGridCellPO> gridCells = xpdGridCellMapper.selectByXpdIdAndGridIdAndDimCombId(
            orgId, xpdId, gridId, xpdGrid.getConfigType() == 1 ? dimCombId : "");
        Validate.isNotEmpty(gridCells, XPD_GRID_CELL_NOT_FOUND);
        Map<String, XpdGridCellPO> gridCellMap =
            StreamUtil.list2map(gridCells, XpdGridCellPO::getId);
        // 获取 宫格内人数

        List<XpdGridRatioPO> gridRatios =
            xpdGridRatioMapper.selectByXpdIdAndGridIdAndDimCombId(
                orgId, xpdId, gridId, xpdGrid.getConfigType() == 1 ? dimCombId : "");
        if (CollectionUtils.isEmpty(gridRatios)) {
            return new ArrayList<>();
        }

        List<CaliIndexNumDTO> caliCellIndexList = calimeetUserMapper.findCaliCellIndexMsg(orgId, caliMeetId);

        // 总人数
        int allUserCount = calimeetUserMapper.countByOrgIdAndCalimeetId(orgId, caliMeetId);
        Map<Integer, Integer> cellUseMap =
            StreamUtil.list2map(caliCellIndexList, CaliIndexNumDTO::getCellIndex, CaliIndexNumDTO::getCellCount);
        List<CaliGridViewVO> resList = new ArrayList<>();
        for (XpdGridRatioPO gridRatio : gridRatios) {
            CaliGridViewVO caliGridView = new CaliGridViewVO();
            String gridCellIds = gridRatio.getGridCellIds();
            String[] cellIds = gridCellIds.split(",");
            List<String> cellNames = new ArrayList<>();
            int cellNum = 0;
            for (String cellId : cellIds) {
                XpdGridCellPO xpdGridCell = gridCellMap.get(cellId);
                if (xpdGridCell != null) {
                    cellNames.add(xpdGridCell.getCellName());
                    Integer indexCount = cellUseMap.get(xpdGridCell.getCellIndex());
                    cellNum = +indexCount;
                }
            }
            caliGridView.setCellName(String.join("\\", cellNames));
            caliGridView.setRatio(gridRatio.getRatio());
            caliGridView.setActualRatio(MathUtil.dividePer(cellNum, allUserCount, 2));
            caliGridView.setOrderIndex(gridRatio.getOrderIndex());
            resList.add(caliGridView);
        }
        return resList;
    }

    public XpdGridPO selectXpdGrid(String orgId, String xpdId) {
        XpdRuleConfPO xpdRuleConf = xpdRuleConfMapper.selectByXpdId(orgId, xpdId);
        isNotNull(xpdRuleConf, XPD_RULE_CONF_NOT_FOUND);
        String gridId = xpdRuleConf.getGridId();
        XpdGridPO xpdGrid = xpdGridMapper.selectByPrimaryKey(gridId);
        isNotNull(xpdGrid, XPD_GRID_NOT_FOUND);
        return xpdGrid;
    }

    public MeetResultDetailVO findGridUser(String orgId, CaliResult4Query query) {
        MeetResultDetailVO res = new MeetResultDetailVO();

        CalimeetPO calimeet = calimeetMapper.selectByIdAndOrgId(query.getMeetingId(), orgId);
        Validate.isNotNull(calimeet, ExceptionKeys.CALI_MEET_NOT_EXISTED);
        res.setCalimeetType(calimeet.getCalimeetType());

        List<PrjUserResultVO> calibrationList = new ArrayList<>();
        List<CaliUserIndexDTO> userIndexList = calimeetUserMapper.findUserIndex(orgId, query.getMeetingId(), query);
        userIndexList = userIndexList.stream().sorted(Comparator.comparing(CaliUserIndexDTO::getCellIndex)).toList();
        // 总人数是校准项目总人数
        int allUserCount = calimeetUserMapper.countByOrgIdAndCalimeetId(orgId, query.getMeetingId());

        XpdGridPO xpdGrid = selectXpdGrid(orgId, query.getProjectId());
        // 格子
        List<XpdGridCellPO> gridCells = xpdGridCellMapper.selectByXpdIdAndGridIdAndDimCombId(
            orgId, query.getProjectId(), xpdGrid.getId(), xpdGrid.getConfigType() == 1 ? query.getDimCombId() : "");
        Map<Integer, XpdGridCellPO> gridCellMap =
            StreamUtil.list2map(gridCells, XpdGridCellPO::getCellIndex);

        List<String> userIds = userIndexList.stream().map(CaliUserIndexDTO::getUserId).toList();
        List<UdpLiteUserPO> udpLiteUsers = udpLiteUserMapper.selectByUserIdsIncludeDelete(orgId, userIds);
        Map<String, UdpLiteUserPO> userMap = StreamUtil.list2map(udpLiteUsers, UdpLiteUserPO::getId);

        // 维度组合
        /*List<XpdGridLevelPO> gridLevels = xpdGridLevelMapper.listByXpdId(orgId, xpdGrid.getXpdId());
        StreamUtil.list2map(gridLevels, XpdGridLevelPO::getOrderIndex)*/

        // 算出每个宫格的人数
        Map<Integer, Long> cellUserMap = userIndexList.stream()
            .collect(Collectors.groupingBy(CaliUserIndexDTO::getCellIndex, Collectors.counting()));

        for (CaliUserIndexDTO caliUserIndex : userIndexList) {
            PrjUserResultVO userResult = new PrjUserResultVO();
            userResult.setUserId(caliUserIndex.getUserId());
            UdpLiteUserPO udpLiteUser = userMap.get(caliUserIndex.getUserId());
            userResult.setFullName(udpLiteUser.getFullname());
            userResult.setUserName(udpLiteUser.getUsername());
            userResult.setImgUrl(udpLiteUser.getImgUrl());
            XpdGridCellPO xpdGridCell = gridCellMap.get(caliUserIndex.getOriginalCellIndex());

            userResult.setXAxis(query.getAxisX());
            userResult.setYAixs(query.getAxisY());
            // 校准前x维度等级
            Integer xIndex = xpdGridCell.getXIndex();

            userResult.setXValue(xIndex);
            // 校准前y维度等级
            userResult.setYValue(xpdGridCell.getYIndex());

            // 校准后
            XpdGridCellPO afterGridCell = gridCellMap.get(caliUserIndex.getCellIndex());
            userResult.setXCheckValue(afterGridCell.getXIndex());
            userResult.setYCheckValue(afterGridCell.getYIndex());
            userResult.setTotalUser((long) allUserCount);
            Long userNum = cellUserMap.get(caliUserIndex.getCellIndex());
            userResult.setPercentStr(MathUtil.dividePerStr(userNum, allUserCount, 2));
            // 计算距离
            userResult.setCaliShift(MathUtil.calculateShortestDistance(xpdGridCell.getXIndex(), xpdGridCell.getYIndex(),
                afterGridCell.getXIndex(), afterGridCell.getYIndex()));
            calibrationList.add(userResult);
        }
        res.setCalibrationList(calibrationList);
        return res;
    }

    public PagingList<CaliCellUserVO> findGridUserPage(String orgId, CaliResult4Query query) {
        PageRequest pageRequest = ApiUtil.getPageRequest(ApiUtil.getRequestByContext());
        IPage<CaliCellUserVO> page = new Page<>(pageRequest.getCurrent(), pageRequest.getSize());
        IPage<CaliCellUserVO> gridUserPage = calimeetUserMapper.findGridUserPage(page, orgId, query);
        return BeanCopierUtil.toPagingList(gridUserPage);

    }

    public CaliDimResultResp moveView(String orgId, CaliMeetMoveCmd cmd) {
        String calimeetId = cmd.getCalimeetId();
        String dimCombId = cmd.getDimCombId();

        XpdDimCombPO xpdDimComb = xpdDimCombMapper.selectByPrimaryKey(dimCombId);

        CalimeetPO calimeet = calimeetMapper.selectByIdAndOrgId(calimeetId, orgId);
        String xpdId = calimeet.getXpdId();
        XpdPO xpd = xpdMapper.selectById(xpdId);
        XpdGridPO xpdGrid = xpdGridMapper.selectByXpdId(orgId, xpdId);

        List<XpdGridCellPO> xpdGridCells = xpdGridCellMapper.listByGridIdAndDimCombId(orgId, xpdGrid.getId(),
            xpdGrid.getConfigType() == 0 ? "" : dimCombId);
        Map<Integer, XpdGridCellPO> gridCellMap =
            StreamUtil.list2map(xpdGridCells, XpdGridCellPO::getCellIndex);

        // 维度分层
        List<XpdGridLevelPO> gridLevels = xpdGridLevelMapper.listByXpdId(orgId, xpdId);
        Map<Integer, String> levelMap =
            StreamUtil.list2map(gridLevels, XpdGridLevelPO::getOrderIndex, XpdGridLevelPO::getId);
        XpdGridCellPO xpdGridCell = gridCellMap.get(cmd.getCellIndex());

        List<CaliUpdateUserResultReq> resultList = new ArrayList<>();
        CaliUpdateUserResultReq xLevel = new CaliUpdateUserResultReq();
        xLevel.setSdDimId(xpdDimComb.getXSdDimId());
        Integer xIndex = xpdGridCell.getXIndex();
        xLevel.setCaliVal(levelMap.get(xIndex));
        resultList.add(xLevel);

        // y维度
        CaliUpdateUserResultReq yLevel = new CaliUpdateUserResultReq();
        yLevel.setSdDimId(xpdDimComb.getYSdDimId());
        Integer yIndex = xpdGridCell.getYIndex();
        yLevel.setCaliVal(levelMap.get(yIndex));
        resultList.add(yLevel);

        xpdResultCalcService.preHandleCaliResult(CaliResultTypeEnum.TYPE_DIM_LEVEL.getType(), resultList);
        return xpdResultCalcService.viewCalcCaliResult(orgId, calimeetId, cmd.getUserId(), resultList);
    }


    public MeetUserDimResultVO getUserResult(
        String orgId, String projectId, String userId, String meetingId, String lang) {
        CalimeetPO calimeet = calimeetMapper.selectByIdAndOrgId(meetingId, orgId);
        Validate.isNotNull(calimeet, ExceptionKeys.CALI_MEET_NOT_EXISTED);
        MeetUserDimResultVO result = new MeetUserDimResultVO();
        UdpLiteUserPO udpUser = udpLiteUserMapper.selectById(userId);
        Validate.isNotNull(udpUser, ExceptionKeys.USER_NOT_EXISTED);
        boolean enableLocalization = l10nAclService.isEnableLocalization(orgId);
        l10nAclService.translateList(enableLocalization, List.of(orgId), lang, UdpLiteUserPO.class, List.of(udpUser));

        result.setUserId(userId);
        result.setDeptName(udpUser.getDeptName());
        result.setImgUrl(udpUser.getImgUrl());
        result.setPosition(udpUser.getPositionName());

        List<XpdDimPO> dimList = xpdDimMapper.listByXpdId(orgId, projectId);
        if (CollectionUtils.isEmpty(dimList)) {
            return result;
        }
        // 校准前
        List<CalimeetResultUserDimPO> resultUserDims =
            calimeetResultUserDimMapper.selectByCaliMeetIdAndUserIds(orgId, calimeet.getId(),
                Lists.newArrayList(userId));
        Map<String, String> beforeDimMap = StreamUtil.list2map(resultUserDims, CalimeetResultUserDimPO::getSdDimId,
            CalimeetResultUserDimPO::getGridLevelId);

        // 校准后
        CaliLastRecordUserDTO lastRecord =
            calimeetUserMapper.findLastRecord(orgId, calimeet.getId(), userId);

        Map<String, String> afterMap = new HashMap<>();
        if (lastRecord != null) {
            String resultDetails = lastRecord.getResultDetails();
            if (StringUtils.isNotEmpty(resultDetails)) {
                CaliDimResultWrapDto
                    dimResults = CommonUtils.tryParseObject(resultDetails, CaliDimResultWrapDto.class);
                List<CaliDimResultDto> dimUserResults = dimResults.getDimResults();
                if (CollectionUtils.isNotEmpty(dimUserResults)) {
                    afterMap = StreamUtil.list2map(dimUserResults, CaliDimResultDto::getSdDimId,
                        CaliDimResultDto::getGridLevelId);
                }
            }
        }

        // 维度分层
        List<XpdGridLevelPO> gridLevels = xpdGridLevelMapper.listByXpdId(orgId, calimeet.getXpdId());
        Map<String, String> levelMap =
            StreamUtil.list2map(gridLevels, XpdGridLevelPO::getId, XpdGridLevelPO::getLevelName);

        // 维度名称
        List<String> dimIds = dimList.stream().map(XpdDimPO::getSdDimId).toList();
        if (CollectionUtils.isEmpty(dimIds)) {
            return result;
        }
        List<DimensionList4Get> baseDimDetails = spsdAclService.getBaseDimDetail(orgId, dimIds);
        Map<String, String> dimNameMap =
            StreamUtil.list2map(baseDimDetails, DimensionList4Get::getId, DimensionList4Get::getDmName);

        List<CaliMeetDimDTO> caliMeetDimList = new ArrayList<>();
        for (XpdDimPO xpdDim : dimList) {
            CaliMeetDimDTO dto = new CaliMeetDimDTO();

            String sdDimId = xpdDim.getSdDimId();
            dto.setDimId(sdDimId);
            dto.setDimensionName(dimNameMap.getOrDefault(sdDimId, ""));
            // 校准前
            String beforelevelId = beforeDimMap.get(sdDimId);
            if (beforelevelId != null) {
                dto.setInitLevelName(levelMap.getOrDefault(beforelevelId, ""));
            }
            // 校准后
            String afterLevelId = afterMap.get(sdDimId);
            if (afterLevelId != null) {
                dto.setLastLevelName(levelMap.getOrDefault(afterLevelId, ""));
            }
            dto.setDimensionType(xpdDim.getDimType());
            caliMeetDimList.add(dto);
        }
        result.setResult(caliMeetDimList);
        return result;
    }
}
